{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\main.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\src\\main.js", "mtime": 1754547969940}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\shop\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nrequire(\"core-js/modules/es6.array.copy-within\");\nrequire(\"core-js/modules/es6.array.fill\");\nrequire(\"core-js/modules/es6.array.find\");\nrequire(\"core-js/modules/es6.array.find-index\");\nrequire(\"core-js/modules/es6.array.from\");\nrequire(\"core-js/modules/es7.array.includes\");\nrequire(\"core-js/modules/es6.array.iterator\");\nrequire(\"core-js/modules/es6.array.of\");\nrequire(\"core-js/modules/es6.array.sort\");\nrequire(\"core-js/modules/es6.array.species\");\nrequire(\"core-js/modules/es6.date.to-primitive\");\nrequire(\"core-js/modules/es6.function.has-instance\");\nrequire(\"core-js/modules/es6.function.name\");\nrequire(\"core-js/modules/es6.map\");\nrequire(\"core-js/modules/es6.math.acosh\");\nrequire(\"core-js/modules/es6.math.asinh\");\nrequire(\"core-js/modules/es6.math.atanh\");\nrequire(\"core-js/modules/es6.math.cbrt\");\nrequire(\"core-js/modules/es6.math.clz32\");\nrequire(\"core-js/modules/es6.math.cosh\");\nrequire(\"core-js/modules/es6.math.expm1\");\nrequire(\"core-js/modules/es6.math.fround\");\nrequire(\"core-js/modules/es6.math.hypot\");\nrequire(\"core-js/modules/es6.math.imul\");\nrequire(\"core-js/modules/es6.math.log1p\");\nrequire(\"core-js/modules/es6.math.log10\");\nrequire(\"core-js/modules/es6.math.log2\");\nrequire(\"core-js/modules/es6.math.sign\");\nrequire(\"core-js/modules/es6.math.sinh\");\nrequire(\"core-js/modules/es6.math.tanh\");\nrequire(\"core-js/modules/es6.math.trunc\");\nrequire(\"core-js/modules/es6.number.constructor\");\nrequire(\"core-js/modules/es6.number.epsilon\");\nrequire(\"core-js/modules/es6.number.is-finite\");\nrequire(\"core-js/modules/es6.number.is-integer\");\nrequire(\"core-js/modules/es6.number.is-nan\");\nrequire(\"core-js/modules/es6.number.is-safe-integer\");\nrequire(\"core-js/modules/es6.number.max-safe-integer\");\nrequire(\"core-js/modules/es6.number.min-safe-integer\");\nrequire(\"core-js/modules/es6.number.parse-float\");\nrequire(\"core-js/modules/es6.number.parse-int\");\nrequire(\"core-js/modules/es6.object.assign\");\nrequire(\"core-js/modules/es7.object.define-getter\");\nrequire(\"core-js/modules/es7.object.define-setter\");\nrequire(\"core-js/modules/es7.object.entries\");\nrequire(\"core-js/modules/es6.object.freeze\");\nrequire(\"core-js/modules/es6.object.get-own-property-descriptor\");\nrequire(\"core-js/modules/es7.object.get-own-property-descriptors\");\nrequire(\"core-js/modules/es6.object.get-own-property-names\");\nrequire(\"core-js/modules/es6.object.get-prototype-of\");\nrequire(\"core-js/modules/es7.object.lookup-getter\");\nrequire(\"core-js/modules/es7.object.lookup-setter\");\nrequire(\"core-js/modules/es6.object.prevent-extensions\");\nrequire(\"core-js/modules/es6.object.is\");\nrequire(\"core-js/modules/es6.object.is-frozen\");\nrequire(\"core-js/modules/es6.object.is-sealed\");\nrequire(\"core-js/modules/es6.object.is-extensible\");\nrequire(\"core-js/modules/es6.object.keys\");\nrequire(\"core-js/modules/es6.object.seal\");\nrequire(\"core-js/modules/es6.object.set-prototype-of\");\nrequire(\"core-js/modules/es7.object.values\");\nrequire(\"core-js/modules/es6.promise\");\nrequire(\"core-js/modules/es7.promise.finally\");\nrequire(\"core-js/modules/es6.reflect.apply\");\nrequire(\"core-js/modules/es6.reflect.construct\");\nrequire(\"core-js/modules/es6.reflect.define-property\");\nrequire(\"core-js/modules/es6.reflect.delete-property\");\nrequire(\"core-js/modules/es6.reflect.get\");\nrequire(\"core-js/modules/es6.reflect.get-own-property-descriptor\");\nrequire(\"core-js/modules/es6.reflect.get-prototype-of\");\nrequire(\"core-js/modules/es6.reflect.has\");\nrequire(\"core-js/modules/es6.reflect.is-extensible\");\nrequire(\"core-js/modules/es6.reflect.own-keys\");\nrequire(\"core-js/modules/es6.reflect.prevent-extensions\");\nrequire(\"core-js/modules/es6.reflect.set\");\nrequire(\"core-js/modules/es6.reflect.set-prototype-of\");\nrequire(\"core-js/modules/es6.regexp.constructor\");\nrequire(\"core-js/modules/es6.regexp.flags\");\nrequire(\"core-js/modules/es6.regexp.match\");\nrequire(\"core-js/modules/es6.regexp.replace\");\nrequire(\"core-js/modules/es6.regexp.split\");\nrequire(\"core-js/modules/es6.regexp.search\");\nrequire(\"core-js/modules/es6.regexp.to-string\");\nrequire(\"core-js/modules/es6.set\");\nrequire(\"core-js/modules/es6.symbol\");\nrequire(\"core-js/modules/es7.symbol.async-iterator\");\nrequire(\"core-js/modules/es6.string.anchor\");\nrequire(\"core-js/modules/es6.string.big\");\nrequire(\"core-js/modules/es6.string.blink\");\nrequire(\"core-js/modules/es6.string.bold\");\nrequire(\"core-js/modules/es6.string.code-point-at\");\nrequire(\"core-js/modules/es6.string.ends-with\");\nrequire(\"core-js/modules/es6.string.fixed\");\nrequire(\"core-js/modules/es6.string.fontcolor\");\nrequire(\"core-js/modules/es6.string.fontsize\");\nrequire(\"core-js/modules/es6.string.from-code-point\");\nrequire(\"core-js/modules/es6.string.includes\");\nrequire(\"core-js/modules/es6.string.italics\");\nrequire(\"core-js/modules/es6.string.iterator\");\nrequire(\"core-js/modules/es6.string.link\");\nrequire(\"core-js/modules/es7.string.pad-start\");\nrequire(\"core-js/modules/es7.string.pad-end\");\nrequire(\"core-js/modules/es6.string.raw\");\nrequire(\"core-js/modules/es6.string.repeat\");\nrequire(\"core-js/modules/es6.string.small\");\nrequire(\"core-js/modules/es6.string.starts-with\");\nrequire(\"core-js/modules/es6.string.strike\");\nrequire(\"core-js/modules/es6.string.sub\");\nrequire(\"core-js/modules/es6.string.sup\");\nrequire(\"core-js/modules/es6.typed.array-buffer\");\nrequire(\"core-js/modules/es6.typed.int8-array\");\nrequire(\"core-js/modules/es6.typed.uint8-array\");\nrequire(\"core-js/modules/es6.typed.uint8-clamped-array\");\nrequire(\"core-js/modules/es6.typed.int16-array\");\nrequire(\"core-js/modules/es6.typed.uint16-array\");\nrequire(\"core-js/modules/es6.typed.int32-array\");\nrequire(\"core-js/modules/es6.typed.uint32-array\");\nrequire(\"core-js/modules/es6.typed.float32-array\");\nrequire(\"core-js/modules/es6.typed.float64-array\");\nrequire(\"core-js/modules/es6.weak-map\");\nrequire(\"core-js/modules/es6.weak-set\");\nrequire(\"core-js/modules/web.timers\");\nrequire(\"core-js/modules/web.immediate\");\nrequire(\"core-js/modules/web.dom.iterable\");\nrequire(\"regenerator-runtime/runtime\");\nvar _vue = _interopRequireDefault(require(\"vue\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nrequire(\"normalize.css/normalize.css\");\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nrequire(\"./styles/element-variables.scss\");\nrequire(\"@/styles/index.scss\");\nrequire(\"@/assets/iconfont/iconfont\");\nrequire(\"@/assets/iconfont/iconfont.css\");\nvar _vueAwesomeSwiper = _interopRequireDefault(require(\"vue-awesome-swiper\"));\nrequire(\"swiper/dist/css/swiper.css\");\nrequire(\"vue-ydui/dist/ydui.base.css\");\nvar _parsing = require(\"@/utils/parsing\");\nvar _vueLazyload = _interopRequireDefault(require(\"vue-lazyload\"));\nvar _App = _interopRequireDefault(require(\"./App\"));\nvar _store = _interopRequireDefault(require(\"./store\"));\nvar _router = _interopRequireDefault(require(\"./router\"));\nvar _attrFrom = _interopRequireDefault(require(\"./components/attrFrom\"));\nvar _uploadFrom = _interopRequireDefault(require(\"./components/uploadPicture/uploadFrom\"));\nvar _goodListFrom = _interopRequireDefault(require(\"./components/goodList/goodListFrom\"));\nvar _couponFrom = _interopRequireDefault(require(\"./components/couponList/couponFrom\"));\nvar _articleFrom = _interopRequireDefault(require(\"./components/articleList/articleFrom\"));\nvar _index2 = _interopRequireDefault(require(\"@/components/uploadPicture/index.vue\"));\nvar _uploadFile = _interopRequireDefault(require(\"@/components/Upload/uploadFile.vue\"));\nvar _iconFrom = _interopRequireDefault(require(\"./components/iconFrom\"));\nvar _TimeSelect = _interopRequireDefault(require(\"@/components/TimeSelect\"));\nvar _dialog = _interopRequireDefault(require(\"@/libs/dialog\"));\nvar _loading = _interopRequireDefault(require(\"@/libs/loading\"));\nvar _asyncValidator = _interopRequireDefault(require(\"async-validator\"));\nvar _index3 = _interopRequireDefault(require(\"@/components/uploadPicture/forGenrator/index.vue\"));\nvar _utils = _interopRequireDefault(require(\"@/utils/utils\"));\nvar _modalAttr = _interopRequireDefault(require(\"@/libs/modal-attr\"));\nvar _modalIcon = _interopRequireDefault(require(\"@/libs/modal-icon\"));\nvar _public = require(\"@/libs/public\");\nvar _timeOptions = _interopRequireDefault(require(\"@/libs/timeOptions\"));\nvar _loadScript = require(\"@/components/FormGenerator/utils/loadScript\");\nrequire(\"./icons\");\nrequire(\"./permission\");\nrequire(\"./utils/error-log\");\nvar filters = _interopRequireWildcard(require(\"./filters\"));\nvar _utils2 = require(\"@/utils\");\nvar Auth = _interopRequireWildcard(require(\"@/libs/wechat\"));\nvar constants = _interopRequireWildcard(require(\"@/utils/constants.js\"));\nvar selfUtil = _interopRequireWildcard(require(\"@/utils/ZBKJIutil.js\"));\nvar _settingMer = _interopRequireDefault(require(\"@/utils/settingMer\"));\nvar _plugins = _interopRequireDefault(require(\"./plugins\"));\nvar _directive = _interopRequireDefault(require(\"./directive\"));\nvar _i18n2 = _interopRequireDefault(require(\"./i18n\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); } // import 'babel-polyfill'\n// a modern alternative to CSS resets\n// global css\n// 懒加载\n_vue.default.config.devtools = true;\n\n// import VueUeditorWrap from 'vue-ueditor-wrap'\n\n// 切勿更改 此组件为表单生成中使用的图片上传组件\n\n// icon\n// permission control\n// error integralLog\n// global filters\n\n//directive\n\n_vue.default.use(_vueLazyload.default, {\n  preLoad: 1.3,\n  error: require('./assets/imgs/no.png'),\n  loading: require('./assets/imgs/moren.jpg'),\n  attempt: 1,\n  listenEvents: ['scroll', 'wheel', 'mousewheel', 'resize', 'animationend', 'transitionend', 'touchmove']\n});\n_vue.default.use(_uploadFrom.default);\n_vue.default.use(_goodListFrom.default);\n_vue.default.use(_couponFrom.default);\n_vue.default.use(_articleFrom.default);\n_vue.default.use(_vueAwesomeSwiper.default);\n_vue.default.use(_plugins.default);\n_vue.default.use(_directive.default);\n_vue.default.component('attrFrom', _attrFrom.default);\n_vue.default.component('UploadIndex', _index2.default);\n_vue.default.component('SelfUpload', _index3.default);\n_vue.default.component('iconFrom', _iconFrom.default);\n_vue.default.component('uploadFile', _uploadFile.default);\n_vue.default.component('timeSelect', _TimeSelect.default);\n_vue.default.prototype.$modalSure = _public.modalSure;\n_vue.default.prototype.$modalAttr = _modalAttr.default;\n_vue.default.prototype.$modalIcon = _modalIcon.default;\n_vue.default.prototype.$dialog = _dialog.default;\n_vue.default.prototype.$scroll = _loading.default;\n_vue.default.prototype.$wechat = Auth;\n_vue.default.prototype.$util = _utils.default;\n_vue.default.prototype.$constants = constants;\n_vue.default.prototype.$selfUtil = selfUtil;\n_vue.default.prototype.$timeOptions = _timeOptions.default;\n_vue.default.prototype.$validator = function (rule) {\n  return new _asyncValidator.default(rule);\n};\n_vue.default.prototype.handleTree = _parsing.handleTree;\n_vue.default.prototype.parseTime = _parsing.parseTime;\n_vue.default.prototype.resetForm = _parsing.resetForm;\nvar cookieName = \"VCONSOLE\";\nvar query = (0, _utils2.parseQuery)();\nvar urlSpread = query[\"spread\"];\nvar vconsole = query[cookieName.toLowerCase()];\nvar md5Crmeb = \"b14d1e9baeced9bb7525ab19ee35f2d2\"; //CRMEB MD5 加密开启vconsole模式\nvar md5UnCrmeb = \"3dca2162c4e101b7656793a1af20295c\"; //UN_CREMB MD5 加密关闭vconsole模式\n\nif (vconsole !== undefined) {\n  if (vconsole === md5UnCrmeb && _jsCookie.default.has(cookieName)) _jsCookie.default.remove(cookieName);\n} else vconsole = _jsCookie.default.get(cookieName);\nif (vconsole !== undefined && vconsole === md5Crmeb) {\n  _jsCookie.default.set(cookieName, md5Crmeb, 3600);\n  var _module = function module() {\n    return Promise.resolve().then(function () {\n      return _interopRequireWildcard(require(\"vconsole\"));\n    });\n  };\n  _module().then(function (Module) {\n    new Module.default();\n  });\n}\n// 自定义实现String 类型的replaceAll方法\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, \"gm\"), s2);\n};\n\n// 配置 Element UI 的国际化和默认大小\n_vue.default.use(_elementUi.default, {\n  size: _jsCookie.default.get('size') || 'mini',\n  // set element-ui default size\n  i18n: function i18n(key, value) {\n    return _i18n2.default.t(key, value);\n  } // 添加国际化配置\n});\n\n// register global utility filters\nObject.keys(filters).forEach(function (key) {\n  _vue.default.filter(key, filters[key]);\n});\n_vue.default.config.productionTip = false;\nvar $previewApp = document.getElementById('previewApp');\nvar childAttrs = {\n  file: '',\n  dialog: ' width=\"600px\" class=\"dialog-width\" v-if=\"visible\" :visible.sync=\"visible\" :modal-append-to-body=\"false\" '\n};\nwindow.addEventListener('message', init, false);\nfunction buildLinks(links) {\n  var strs = '';\n  links.forEach(function (url) {\n    strs += \"<link href=\\\"\".concat(url, \"\\\" rel=\\\"stylesheet\\\">\");\n  });\n  return strs;\n}\nfunction init(event) {\n  if (event.data.type === 'refreshFrame') {\n    var code = event.data.data;\n    var attrs = childAttrs[code.generateConf.type];\n    var links = '';\n    if (Array.isArray(code.links) && code.links.length > 0) {\n      links = buildLinks(code.links);\n    }\n    $previewApp.innerHTML = \"\".concat(links, \"<style>\").concat(code.css, \"</style><div id=\\\"app\\\"></div>\");\n    if (Array.isArray(code.scripts) && code.scripts.length > 0) {\n      (0, _loadScript.loadScriptQueue)(code.scripts, function () {\n        newVue(attrs, code.js, code.html);\n      });\n    } else {\n      newVue(attrs, code.js, code.html);\n    }\n  }\n}\nfunction newVue(attrs, main, html) {\n  // eslint-disable-next-line no-eval\n  main = eval(\"(\".concat(main, \")\"));\n  main.template = \"<div>\".concat(html, \"</div>\");\n  new _vue.default({\n    components: {\n      child: main\n    },\n    data: function data() {\n      return {\n        visible: true\n      };\n    },\n    template: \"<div><child \".concat(attrs, \"/></div>\")\n  }).$mount('#app');\n}\nString.prototype.replaceAll = function (s1, s2) {\n  return this.replace(new RegExp(s1, \"gm\"), s2);\n};\n\n// 添加crmeb chat 统计\nvar __s = document.createElement('script');\n__s.src = \"\".concat(_settingMer.default.apiBaseURL, \"/public/jsconfig/getcrmebchatconfig\");\ndocument.head.appendChild(__s);\nvar _hmt = _hmt || [];\n(function () {\n  var hm = document.createElement(\"script\");\n  hm.src = \"https://cdn.oss.9gt.net/js/es.js?version=JAVA-KY-v1.3.4\";\n  var s = document.getElementsByTagName(\"script\")[0];\n  s.parentNode.insertBefore(hm, s);\n})();\nnew _vue.default({\n  el: '#app',\n  router: _router.default,\n  store: _store.default,\n  i18n: _i18n2.default,\n  render: function render(h) {\n    return h(_App.default);\n  }\n});", null]}