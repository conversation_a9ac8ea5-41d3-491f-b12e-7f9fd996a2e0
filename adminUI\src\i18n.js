import Vue from 'vue'
import VueI18n from 'vue-i18n'
import zhCN from './lang/zh-CN'
import en from './lang/en'
import id from './lang/id'
// Element UI 语言包
import elementZhCN from 'element-ui/lib/locale/lang/zh-C<PERSON>'
import elementEn from 'element-ui/lib/locale/lang/en'
import elementId from 'element-ui/lib/locale/lang/id'
import locale from 'element-ui/lib/locale'

Vue.use(VueI18n)

const messages = {
  'zh-CN': zhCN,
  en: en,
  id: id
}

// Element UI 语言包映射
const elementLocales = {
  'zh-CN': elementZhCN,
  'en': elementEn,
  'id': elementId
}

const i18n = new VueI18n({
 locale: localStorage.getItem('locale') || 'zh-CN',
  fallbackLocale: 'zh-CN',
  messages
})

// 设置 Element UI 初始语言
const currentLocale = localStorage.getItem('locale') || 'zh-CN'
locale.use(elementLocales[currentLocale])

// 添加语言切换方法到 i18n 实例
i18n.setElementLocale = function(newLocale) {
  if (elementLocales[newLocale]) {
    locale.use(elementLocales[newLocale])
    // 强制更新所有 Element UI 组件
    this.locale = newLocale
  }
}

export default i18n
